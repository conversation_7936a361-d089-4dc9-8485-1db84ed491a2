
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + this.currentLevel);
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelNumber: this.currentLevel
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        cc.log("\u6536\u5230\u5173\u5361\u4FE1\u606F - \u5F53\u524D\u5173\u5361 " + this.currentLevel, levelInfo);
        cc.log("\u540E\u7AEF\u8FD4\u56DE\u7684levelId: " + levelInfo.levelId + ", levelNumber: " + levelInfo.levelNumber);
        this.currentLevelInfo = levelInfo;
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        cc.log("\u4F7F\u7528\u5173\u5361\u7F16\u53F7 " + this.currentLevel + " \u8FDB\u5165\u5173\u5361");
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
            cc.log("\u66F4\u65B0\u5730\u96F7\u6570UI: " + mineCount);
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        cc.log("\u8FDB\u5165\u5173\u5361 " + levelNumber);
        // 调试：检查地图节点是否已配置
        cc.log("地图节点配置状态：");
        cc.log("qipan8x8Node: " + (this.qipan8x8Node ? '✅' : '❌'));
        cc.log("qipan8x9Node: " + (this.qipan8x9Node ? '✅' : '❌'));
        cc.log("qipan9x9Node: " + (this.qipan9x9Node ? '✅' : '❌'));
        cc.log("qipan9x10Node: " + (this.qipan9x10Node ? '✅' : '❌'));
        cc.log("qipan10x10Node: " + (this.qipan10x10Node ? '✅' : '❌'));
        cc.log("levelS001Node: " + (this.levelS001Node ? '✅' : '❌'));
        // 先隐藏所有地图容器
        this.hideAllMapContainers();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            cc.log("\uD83D\uDD0D \u51C6\u5907\u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName);
            cc.log("\u8282\u70B9\u8DEF\u5F84: " + mapNode.name);
            cc.log("\u8282\u70B9\u5F53\u524D\u72B6\u6001 - active: " + mapNode.active + ", parent: " + (mapNode.parent ? mapNode.parent.name : 'null'));
            mapNode.active = true;
            // 检查父节点链是否都是激活状态
            var currentNode = mapNode.parent;
            var parentChain = [];
            while (currentNode) {
                parentChain.push(currentNode.name + "(" + currentNode.active + ")");
                currentNode = currentNode.parent;
            }
            cc.log("\u7236\u8282\u70B9\u94FE: " + parentChain.join(' -> '));
            cc.log("\u2705 \u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName + " - \u8BBE\u7F6E\u540E\u72B6\u6001: " + mapNode.active);
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        this.currentLevel = levelNumber;
        cc.log("\u8BBE\u7F6E\u5F53\u524D\u5173\u5361: " + levelNumber);
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    /**
     * 隐藏所有地图容器
     */
    LevelPageController.prototype.hideAllMapContainers = function () {
        // 确保 level_page 节点是激活的
        if (this.levelPageNode) {
            this.levelPageNode.active = true;
            cc.log("✅ 确保 level_page 节点激活");
        }
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            cc.log("隐藏 game_map_1 容器");
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
            cc.log("隐藏 game_map_2 容器");
        }
        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    };
    /**
     * 显示 game_map_1 容器（方形地图）
     */
    LevelPageController.prototype.showGameMap1 = function () {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            cc.log("✅ 显示 game_map_1 容器");
        }
        else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    };
    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    LevelPageController.prototype.showGameMap2 = function () {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
            cc.log("✅ 显示 game_map_2 容器");
        }
        else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    };
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImFzc2V0cy9zY3JpcHRzL2xldmVsL0xldmVsUGFnZUNvbnRyb2xsZXIudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLG9CQUFvQjtBQUNwQiw0RUFBNEU7QUFDNUUsbUJBQW1CO0FBQ25CLHNGQUFzRjtBQUN0Riw4QkFBOEI7QUFDOUIsc0ZBQXNGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFHdEYsOENBQTZDO0FBQzdDLDREQUEyRDtBQUVyRCxJQUFBLEtBQXdCLEVBQUUsQ0FBQyxVQUFVLEVBQW5DLE9BQU8sYUFBQSxFQUFFLFFBQVEsY0FBa0IsQ0FBQztBQUc1QztJQUFpRCx1Q0FBWTtJQUE3RDtRQUFBLHFFQTZUQztRQTNURyxTQUFTO1FBRVQscUJBQWUsR0FBYyxJQUFJLENBQUM7UUFFbEMsVUFBVTtRQUVWLG9CQUFjLEdBQWEsSUFBSSxDQUFDO1FBRWhDLGVBQWU7UUFFZixtQkFBYSxHQUFZLElBQUksQ0FBQztRQUU5QixlQUFlO1FBRWYsa0JBQVksR0FBWSxJQUFJLENBQUM7UUFFN0IsZUFBZTtRQUVmLGtCQUFZLEdBQVksSUFBSSxDQUFDO1FBRTdCLFdBQVc7UUFFWCxrQkFBWSxHQUFZLElBQUksQ0FBQyxDQUFDLDBDQUEwQztRQUd4RSxrQkFBWSxHQUFZLElBQUksQ0FBQyxDQUFDLDBDQUEwQztRQUd4RSxrQkFBWSxHQUFZLElBQUksQ0FBQyxDQUFDLDBDQUEwQztRQUd4RSxtQkFBYSxHQUFZLElBQUksQ0FBQyxDQUFDLDJDQUEyQztRQUcxRSxvQkFBYyxHQUFZLElBQUksQ0FBQyxDQUFDLDRDQUE0QztRQUU1RSxXQUFXO1FBRVgsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFHMUUsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFHMUUsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFHMUUsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFHMUUsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFHMUUsbUJBQWEsR0FBWSxJQUFJLENBQUMsQ0FBQywyQ0FBMkM7UUFFMUUsU0FBUztRQUNELGtCQUFZLEdBQVcsQ0FBQyxDQUFDO1FBQ3pCLHNCQUFnQixHQUE0QixJQUFJLENBQUM7O0lBa1E3RCxDQUFDO0lBaFFHLG9DQUFNLEdBQU47UUFDSSxlQUFlO1FBQ2YsSUFBSSxJQUFJLENBQUMsZUFBZSxFQUFFO1lBQ3RCLElBQUksQ0FBQyxlQUFlLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxPQUFPLEVBQUUsSUFBSSxDQUFDLHNCQUFzQixFQUFFLElBQUksQ0FBQyxDQUFDO1NBQzVFO0lBQ0wsQ0FBQztJQUVELG1DQUFLLEdBQUw7UUFDSSxlQUFlO1FBQ2YsSUFBSSxDQUFDLGVBQWUsRUFBRSxDQUFDO0lBQzNCLENBQUM7SUFFRDs7T0FFRztJQUNLLG9EQUFzQixHQUE5QjtRQUNJLEVBQUUsQ0FBQyxHQUFHLENBQUMsNkNBQWEsSUFBSSxDQUFDLFlBQWMsQ0FBQyxDQUFDO1FBRXpDLCtCQUErQjtRQUMvQixJQUFNLE9BQU8sR0FBMkI7WUFDcEMsV0FBVyxFQUFFLElBQUksQ0FBQyxZQUFZO1NBQ2pDLENBQUM7UUFFRixtQ0FBZ0IsQ0FBQyxXQUFXLEVBQUUsQ0FBQyxPQUFPLENBQUMscUJBQVMsQ0FBQyxzQkFBc0IsRUFBRSxPQUFPLENBQUMsQ0FBQztJQUN0RixDQUFDO0lBRUQ7OztPQUdHO0lBQ0ksK0NBQWlCLEdBQXhCLFVBQXlCLFNBQWtDO1FBQ3ZELEVBQUUsQ0FBQyxHQUFHLENBQUMscUVBQWlCLElBQUksQ0FBQyxZQUFjLEVBQUUsU0FBUyxDQUFDLENBQUM7UUFDeEQsRUFBRSxDQUFDLEdBQUcsQ0FBQyw0Q0FBaUIsU0FBUyxDQUFDLE9BQU8sdUJBQWtCLFNBQVMsQ0FBQyxXQUFhLENBQUMsQ0FBQztRQUVwRixJQUFJLENBQUMsZ0JBQWdCLEdBQUcsU0FBUyxDQUFDO1FBRWxDLFVBQVU7UUFDVixJQUFJLENBQUMsaUJBQWlCLENBQUMsU0FBUyxDQUFDLFNBQVMsQ0FBQyxDQUFDO1FBRTVDLDhCQUE4QjtRQUM5Qiw0QkFBNEI7UUFDNUIsRUFBRSxDQUFDLEdBQUcsQ0FBQywwQ0FBVSxJQUFJLENBQUMsWUFBWSw4QkFBTyxDQUFDLENBQUM7UUFDM0MsSUFBSSxDQUFDLFVBQVUsQ0FBQyxJQUFJLENBQUMsWUFBWSxDQUFDLENBQUM7SUFDdkMsQ0FBQztJQUVEOzs7T0FHRztJQUNLLCtDQUFpQixHQUF6QixVQUEwQixTQUFpQjtRQUN2QyxJQUFJLElBQUksQ0FBQyxjQUFjLEVBQUU7WUFDckIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxNQUFNLEdBQUcsU0FBUyxDQUFDLFFBQVEsRUFBRSxDQUFDO1lBQ2xELEVBQUUsQ0FBQyxHQUFHLENBQUMsdUNBQVksU0FBVyxDQUFDLENBQUM7U0FDbkM7SUFDTCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ssd0NBQVUsR0FBbEIsVUFBbUIsV0FBbUI7UUFDbEMsRUFBRSxDQUFDLEdBQUcsQ0FBQyw4QkFBUSxXQUFhLENBQUMsQ0FBQztRQUU5QixpQkFBaUI7UUFDakIsRUFBRSxDQUFDLEdBQUcsQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUNwQixFQUFFLENBQUMsR0FBRyxDQUFDLG9CQUFpQixJQUFJLENBQUMsWUFBWSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBRSxDQUFDLENBQUM7UUFDekQsRUFBRSxDQUFDLEdBQUcsQ0FBQyxvQkFBaUIsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUUsQ0FBQyxDQUFDO1FBQ3pELEVBQUUsQ0FBQyxHQUFHLENBQUMsb0JBQWlCLElBQUksQ0FBQyxZQUFZLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFFLENBQUMsQ0FBQztRQUN6RCxFQUFFLENBQUMsR0FBRyxDQUFDLHFCQUFrQixJQUFJLENBQUMsYUFBYSxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBRSxDQUFDLENBQUM7UUFDM0QsRUFBRSxDQUFDLEdBQUcsQ0FBQyxzQkFBbUIsSUFBSSxDQUFDLGNBQWMsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxHQUFHLENBQUUsQ0FBQyxDQUFDO1FBQzdELEVBQUUsQ0FBQyxHQUFHLENBQUMscUJBQWtCLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFFLENBQUMsQ0FBQztRQUUzRCxZQUFZO1FBQ1osSUFBSSxDQUFDLG9CQUFvQixFQUFFLENBQUM7UUFFNUIsaUJBQWlCO1FBQ2pCLElBQUksV0FBVyxJQUFJLENBQUMsSUFBSSxXQUFXLElBQUksQ0FBQyxFQUFFO1lBQ3RDLGtEQUFrRDtZQUNsRCxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDcEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsWUFBWSxFQUFFLFVBQVUsQ0FBQyxDQUFDO1NBQ25EO2FBQU0sSUFBSSxXQUFXLEtBQUssQ0FBQyxFQUFFO1lBQzFCLGlEQUFpRDtZQUNqRCxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDcEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLFlBQVksQ0FBQyxDQUFDO1NBQ3REO2FBQU0sSUFBSSxXQUFXLElBQUksQ0FBQyxJQUFJLFdBQVcsSUFBSSxDQUFDLEVBQUU7WUFDN0Msa0RBQWtEO1lBQ2xELElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxZQUFZLEVBQUUsVUFBVSxDQUFDLENBQUM7U0FDbkQ7YUFBTSxJQUFJLFdBQVcsS0FBSyxFQUFFLEVBQUU7WUFDM0Isa0RBQWtEO1lBQ2xELElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsWUFBWSxDQUFDLENBQUM7U0FDdEQ7YUFBTSxJQUFJLFdBQVcsSUFBSSxFQUFFLElBQUksV0FBVyxJQUFJLEVBQUUsRUFBRTtZQUMvQyxvREFBb0Q7WUFDcEQsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLFlBQVksRUFBRSxVQUFVLENBQUMsQ0FBQztTQUNuRDthQUFNLElBQUksV0FBVyxLQUFLLEVBQUUsRUFBRTtZQUMzQixrREFBa0Q7WUFDbEQsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxZQUFZLENBQUMsQ0FBQztTQUN0RDthQUFNLElBQUksV0FBVyxJQUFJLEVBQUUsSUFBSSxXQUFXLElBQUksRUFBRSxFQUFFO1lBQy9DLHFEQUFxRDtZQUNyRCxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDcEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLFdBQVcsQ0FBQyxDQUFDO1NBQ3JEO2FBQU0sSUFBSSxXQUFXLEtBQUssRUFBRSxFQUFFO1lBQzNCLGtEQUFrRDtZQUNsRCxJQUFJLENBQUMsWUFBWSxFQUFFLENBQUM7WUFDcEIsSUFBSSxDQUFDLFdBQVcsQ0FBQyxJQUFJLENBQUMsYUFBYSxFQUFFLFlBQVksQ0FBQyxDQUFDO1NBQ3REO2FBQU0sSUFBSSxXQUFXLElBQUksRUFBRSxJQUFJLFdBQVcsSUFBSSxFQUFFLEVBQUU7WUFDL0Msc0RBQXNEO1lBQ3RELElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxjQUFjLEVBQUUsWUFBWSxDQUFDLENBQUM7U0FDdkQ7YUFBTSxJQUFJLFdBQVcsS0FBSyxFQUFFLEVBQUU7WUFDM0Isa0RBQWtEO1lBQ2xELElBQUksQ0FBQyxZQUFZLEVBQUUsQ0FBQztZQUNwQixJQUFJLENBQUMsV0FBVyxDQUFDLElBQUksQ0FBQyxhQUFhLEVBQUUsWUFBWSxDQUFDLENBQUM7U0FDdEQ7YUFBTSxJQUFJLFdBQVcsSUFBSSxFQUFFLElBQUksV0FBVyxJQUFJLEVBQUUsRUFBRTtZQUMvQyxzREFBc0Q7WUFDdEQsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGNBQWMsRUFBRSxZQUFZLENBQUMsQ0FBQztTQUN2RDthQUFNLElBQUksV0FBVyxLQUFLLEVBQUUsRUFBRTtZQUMzQixrREFBa0Q7WUFDbEQsSUFBSSxDQUFDLFlBQVksRUFBRSxDQUFDO1lBQ3BCLElBQUksQ0FBQyxXQUFXLENBQUMsSUFBSSxDQUFDLGFBQWEsRUFBRSxZQUFZLENBQUMsQ0FBQztTQUN0RDthQUFNO1lBQ0gsRUFBRSxDQUFDLElBQUksQ0FBQyxpREFBWSxXQUFhLENBQUMsQ0FBQztTQUN0QztJQUNMLENBQUM7SUFFRDs7OztPQUlHO0lBQ0sseUNBQVcsR0FBbkIsVUFBb0IsT0FBZ0IsRUFBRSxPQUFlO1FBQ2pELElBQUksT0FBTyxFQUFFO1lBQ1QsRUFBRSxDQUFDLEdBQUcsQ0FBQyxvRUFBZ0IsT0FBUyxDQUFDLENBQUM7WUFDbEMsRUFBRSxDQUFDLEdBQUcsQ0FBQywrQkFBUyxPQUFPLENBQUMsSUFBTSxDQUFDLENBQUM7WUFDaEMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxvREFBb0IsT0FBTyxDQUFDLE1BQU0sbUJBQWEsT0FBTyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLE1BQU0sQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLE1BQU0sQ0FBRSxDQUFDLENBQUM7WUFFdkcsT0FBTyxDQUFDLE1BQU0sR0FBRyxJQUFJLENBQUM7WUFFdEIsaUJBQWlCO1lBQ2pCLElBQUksV0FBVyxHQUFHLE9BQU8sQ0FBQyxNQUFNLENBQUM7WUFDakMsSUFBSSxXQUFXLEdBQUcsRUFBRSxDQUFDO1lBQ3JCLE9BQU8sV0FBVyxFQUFFO2dCQUNoQixXQUFXLENBQUMsSUFBSSxDQUFJLFdBQVcsQ0FBQyxJQUFJLFNBQUksV0FBVyxDQUFDLE1BQU0sTUFBRyxDQUFDLENBQUM7Z0JBQy9ELFdBQVcsR0FBRyxXQUFXLENBQUMsTUFBTSxDQUFDO2FBQ3BDO1lBQ0QsRUFBRSxDQUFDLEdBQUcsQ0FBQywrQkFBUyxXQUFXLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBRyxDQUFDLENBQUM7WUFFNUMsRUFBRSxDQUFDLEdBQUcsQ0FBQyxrREFBYSxPQUFPLDJDQUFhLE9BQU8sQ0FBQyxNQUFRLENBQUMsQ0FBQztTQUM3RDthQUFNO1lBQ0gsRUFBRSxDQUFDLElBQUksQ0FBQyx3REFBYyxPQUFTLENBQUMsQ0FBQztZQUNqQyxFQUFFLENBQUMsSUFBSSxDQUFDLGlGQUFrQyxPQUFPLDhCQUFPLENBQUMsQ0FBQztTQUM3RDtJQUNMLENBQUM7SUFFRDs7T0FFRztJQUNLLDZDQUFlLEdBQXZCO1FBQ0ksSUFBTSxXQUFXLEdBQUc7WUFDaEIsSUFBSSxDQUFDLFlBQVk7WUFDakIsSUFBSSxDQUFDLFlBQVk7WUFDakIsSUFBSSxDQUFDLFlBQVk7WUFDakIsSUFBSSxDQUFDLGFBQWE7WUFDbEIsSUFBSSxDQUFDLGNBQWM7WUFDbkIsSUFBSSxDQUFDLGFBQWE7WUFDbEIsSUFBSSxDQUFDLGFBQWE7WUFDbEIsSUFBSSxDQUFDLGFBQWE7WUFDbEIsSUFBSSxDQUFDLGFBQWE7WUFDbEIsSUFBSSxDQUFDLGFBQWE7WUFDbEIsSUFBSSxDQUFDLGFBQWE7U0FDckIsQ0FBQztRQUVGLFdBQVcsQ0FBQyxPQUFPLENBQUMsVUFBQSxJQUFJO1lBQ3BCLElBQUksSUFBSSxFQUFFO2dCQUNOLElBQUksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO2FBQ3ZCO1FBQ0wsQ0FBQyxDQUFDLENBQUM7SUFDUCxDQUFDO0lBRUQ7OztPQUdHO0lBQ0ksNkNBQWUsR0FBdEIsVUFBdUIsV0FBbUI7UUFDdEMsSUFBSSxDQUFDLFlBQVksR0FBRyxXQUFXLENBQUM7UUFDaEMsRUFBRSxDQUFDLEdBQUcsQ0FBQywyQ0FBVyxXQUFhLENBQUMsQ0FBQztRQUVqQyxnQkFBZ0I7UUFDaEIsSUFBSSxDQUFDLFVBQVUsQ0FBQyxXQUFXLENBQUMsQ0FBQztJQUNqQyxDQUFDO0lBRUQ7O09BRUc7SUFDSSw2Q0FBZSxHQUF0QjtRQUNJLE9BQU8sSUFBSSxDQUFDLFlBQVksQ0FBQztJQUM3QixDQUFDO0lBRUQ7O09BRUc7SUFDSSxpREFBbUIsR0FBMUI7UUFDSSxPQUFPLElBQUksQ0FBQyxnQkFBZ0IsQ0FBQztJQUNqQyxDQUFDO0lBRUQ7O09BRUc7SUFDSyxrREFBb0IsR0FBNUI7UUFDSSx1QkFBdUI7UUFDdkIsSUFBSSxJQUFJLENBQUMsYUFBYSxFQUFFO1lBQ3BCLElBQUksQ0FBQyxhQUFhLENBQUMsTUFBTSxHQUFHLElBQUksQ0FBQztZQUNqQyxFQUFFLENBQUMsR0FBRyxDQUFDLHNCQUFzQixDQUFDLENBQUM7U0FDbEM7UUFFRCxjQUFjO1FBQ2QsSUFBSSxJQUFJLENBQUMsWUFBWSxFQUFFO1lBQ25CLElBQUksQ0FBQyxZQUFZLENBQUMsTUFBTSxHQUFHLEtBQUssQ0FBQztZQUNqQyxFQUFFLENBQUMsR0FBRyxDQUFDLGtCQUFrQixDQUFDLENBQUM7U0FDOUI7UUFDRCxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsS0FBSyxDQUFDO1lBQ2pDLEVBQUUsQ0FBQyxHQUFHLENBQUMsa0JBQWtCLENBQUMsQ0FBQztTQUM5QjtRQUVELGdCQUFnQjtRQUNoQixJQUFJLENBQUMsZUFBZSxFQUFFLENBQUM7SUFDM0IsQ0FBQztJQUVEOztPQUVHO0lBQ0ssMENBQVksR0FBcEI7UUFDSSxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1lBQ2hDLEVBQUUsQ0FBQyxHQUFHLENBQUMsb0JBQW9CLENBQUMsQ0FBQztTQUNoQzthQUFNO1lBQ0gsRUFBRSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1NBQ2pDO0lBQ0wsQ0FBQztJQUVEOztPQUVHO0lBQ0ssMENBQVksR0FBcEI7UUFDSSxJQUFJLElBQUksQ0FBQyxZQUFZLEVBQUU7WUFDbkIsSUFBSSxDQUFDLFlBQVksQ0FBQyxNQUFNLEdBQUcsSUFBSSxDQUFDO1lBQ2hDLEVBQUUsQ0FBQyxHQUFHLENBQUMsb0JBQW9CLENBQUMsQ0FBQztTQUNoQzthQUFNO1lBQ0gsRUFBRSxDQUFDLElBQUksQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO1NBQ2pDO0lBQ0wsQ0FBQztJQXhURDtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsTUFBTSxDQUFDO2dFQUNjO0lBSWxDO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxLQUFLLENBQUM7K0RBQ2E7SUFJaEM7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUk5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzZEQUNXO0lBSTdCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NkRBQ1c7SUFJN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs2REFDVztJQUc3QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzZEQUNXO0lBRzdCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7NkRBQ1c7SUFHN0I7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOytEQUNhO0lBSS9CO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7OERBQ1k7SUFHOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzhEQUNZO0lBRzlCO1FBREMsUUFBUSxDQUFDLEVBQUUsQ0FBQyxJQUFJLENBQUM7OERBQ1k7SUFHOUI7UUFEQyxRQUFRLENBQUMsRUFBRSxDQUFDLElBQUksQ0FBQzs4REFDWTtJQUc5QjtRQURDLFFBQVEsQ0FBQyxFQUFFLENBQUMsSUFBSSxDQUFDOzhEQUNZO0lBdkRiLG1CQUFtQjtRQUR2QyxPQUFPO09BQ2EsbUJBQW1CLENBNlR2QztJQUFELDBCQUFDO0NBN1RELEFBNlRDLENBN1RnRCxFQUFFLENBQUMsU0FBUyxHQTZUNUQ7a0JBN1RvQixtQkFBbUIiLCJmaWxlIjoiIiwic291cmNlUm9vdCI6Ii8iLCJzb3VyY2VzQ29udGVudCI6WyIvLyBMZWFybiBUeXBlU2NyaXB0OlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3R5cGVzY3JpcHQuaHRtbFxuLy8gTGVhcm4gQXR0cmlidXRlOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL3JlZmVyZW5jZS9hdHRyaWJ1dGVzLmh0bWxcbi8vIExlYXJuIGxpZmUtY3ljbGUgY2FsbGJhY2tzOlxuLy8gIC0gaHR0cHM6Ly9kb2NzLmNvY29zLmNvbS9jcmVhdG9yLzIuNC9tYW51YWwvZW4vc2NyaXB0aW5nL2xpZmUtY3ljbGUtY2FsbGJhY2tzLmh0bWxcblxuaW1wb3J0IHsgRXh0ZW5kTGV2ZWxJbmZvUmVxdWVzdCwgRXh0ZW5kTGV2ZWxJbmZvUmVzcG9uc2UgfSBmcm9tIFwiLi4vYmVhbi9HYW1lQmVhblwiO1xuaW1wb3J0IHsgTWVzc2FnZUlkIH0gZnJvbSBcIi4uL25ldC9NZXNzYWdlSWRcIjtcbmltcG9ydCB7IFdlYlNvY2tldE1hbmFnZXIgfSBmcm9tIFwiLi4vbmV0L1dlYlNvY2tldE1hbmFnZXJcIjtcblxuY29uc3QgeyBjY2NsYXNzLCBwcm9wZXJ0eSB9ID0gY2MuX2RlY29yYXRvcjtcblxuQGNjY2xhc3NcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIExldmVsUGFnZUNvbnRyb2xsZXIgZXh0ZW5kcyBjYy5Db21wb25lbnQge1xuXG4gICAgLy8g5byA5aeL5ri45oiP5oyJ6ZKuXG4gICAgQHByb3BlcnR5KGNjLkJ1dHRvbilcbiAgICBzdGFydEdhbWVCdXR0b246IGNjLkJ1dHRvbiA9IG51bGw7XG5cbiAgICAvLyDlnLDpm7fmlbBVSeagh+etvlxuICAgIEBwcm9wZXJ0eShjYy5MYWJlbClcbiAgICBtaW5lQ291bnRMYWJlbDogY2MuTGFiZWwgPSBudWxsO1xuXG4gICAgLy8gbGV2ZWxfcGFnZeiKgueCuVxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxldmVsUGFnZU5vZGU6IGNjLk5vZGUgPSBudWxsO1xuXG4gICAgLy8gZ2FtZV9tYXBfMeiKgueCuVxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGdhbWVNYXAxTm9kZTogY2MuTm9kZSA9IG51bGw7XG5cbiAgICAvLyBnYW1lX21hcF8y6IqC54K5XG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgZ2FtZU1hcDJOb2RlOiBjYy5Ob2RlID0gbnVsbDtcblxuICAgIC8vIOaWueW9ouWcsOWbvuiKgueCueW8leeUqFxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOHg4Tm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMS9jaGVzc19iZy9xaXBhbjgqOFxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgcWlwYW44eDlOb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8xL2NoZXNzX2JnL3FpcGFuOCo5XG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBxaXBhbjl4OU5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW45KjlcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIHFpcGFuOXgxME5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW45KjEwXG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBxaXBhbjEweDEwTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMS9jaGVzc19iZy9xaXBhbjEwKjEwXG5cbiAgICAvLyDnibnmrorlhbPljaHoioLngrnlvJXnlKhcbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBsZXZlbFMwMDFOb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8yL2dhbWVfYmcvTGV2ZWxfUzAwMVxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgbGV2ZWxTMDAyTm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMi9nYW1lX2JnL0xldmVsX1MwMDJcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxldmVsUzAwM05vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDAzXG5cbiAgICBAcHJvcGVydHkoY2MuTm9kZSlcbiAgICBsZXZlbFMwMDROb2RlOiBjYy5Ob2RlID0gbnVsbDsgLy8gbGV2ZWxfcGFnZS9nYW1lX21hcF8yL2dhbWVfYmcvTGV2ZWxfUzAwNFxuXG4gICAgQHByb3BlcnR5KGNjLk5vZGUpXG4gICAgbGV2ZWxTMDA1Tm9kZTogY2MuTm9kZSA9IG51bGw7IC8vIGxldmVsX3BhZ2UvZ2FtZV9tYXBfMi9nYW1lX2JnL0xldmVsX1MwMDVcblxuICAgIEBwcm9wZXJ0eShjYy5Ob2RlKVxuICAgIGxldmVsUzAwNk5vZGU6IGNjLk5vZGUgPSBudWxsOyAvLyBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDA2XG5cbiAgICAvLyDlvZPliY3lhbPljaHmlbDmja5cbiAgICBwcml2YXRlIGN1cnJlbnRMZXZlbDogbnVtYmVyID0gMTtcbiAgICBwcml2YXRlIGN1cnJlbnRMZXZlbEluZm86IEV4dGVuZExldmVsSW5mb1Jlc3BvbnNlID0gbnVsbDtcblxuICAgIG9uTG9hZCgpIHtcbiAgICAgICAgLy8g6K6+572u5byA5aeL5ri45oiP5oyJ6ZKu54K55Ye75LqL5Lu2XG4gICAgICAgIGlmICh0aGlzLnN0YXJ0R2FtZUJ1dHRvbikge1xuICAgICAgICAgICAgdGhpcy5zdGFydEdhbWVCdXR0b24ubm9kZS5vbignY2xpY2snLCB0aGlzLm9uU3RhcnRHYW1lQnV0dG9uQ2xpY2ssIHRoaXMpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgc3RhcnQoKSB7XG4gICAgICAgIC8vIOWIneWni+WMluaXtumakOiXj+aJgOacieWcsOWbvuiKgueCuVxuICAgICAgICB0aGlzLmhpZGVBbGxNYXBOb2RlcygpO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOW8gOWni+a4uOaIj+aMiemSrueCueWHu+S6i+S7tlxuICAgICAqL1xuICAgIHByaXZhdGUgb25TdGFydEdhbWVCdXR0b25DbGljaygpIHtcbiAgICAgICAgY2MubG9nKGDlvIDlp4vmuLjmiI8gLSDlhbPljaEgJHt0aGlzLmN1cnJlbnRMZXZlbH1gKTtcbiAgICAgICAgXG4gICAgICAgIC8vIOWPkemAgUV4dGVuZExldmVsSW5mb+a2iOaBr+WIsOWQjuerr+iOt+WPluWcsOWbvuaVsOaNrlxuICAgICAgICBjb25zdCByZXF1ZXN0OiBFeHRlbmRMZXZlbEluZm9SZXF1ZXN0ID0ge1xuICAgICAgICAgICAgbGV2ZWxOdW1iZXI6IHRoaXMuY3VycmVudExldmVsXG4gICAgICAgIH07XG4gICAgICAgIFxuICAgICAgICBXZWJTb2NrZXRNYW5hZ2VyLkdldEluc3RhbmNlKCkuc2VuZE1zZyhNZXNzYWdlSWQuTXNnVHlwZUV4dGVuZExldmVsSW5mbywgcmVxdWVzdCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5aSE55CGRXh0ZW5kTGV2ZWxJbmZv5ZON5bqUXG4gICAgICogQHBhcmFtIGxldmVsSW5mbyDlhbPljaHkv6Hmga/lk43lupTmlbDmja5cbiAgICAgKi9cbiAgICBwdWJsaWMgb25FeHRlbmRMZXZlbEluZm8obGV2ZWxJbmZvOiBFeHRlbmRMZXZlbEluZm9SZXNwb25zZSkge1xuICAgICAgICBjYy5sb2coYOaUtuWIsOWFs+WNoeS/oeaBryAtIOW9k+WJjeWFs+WNoSAke3RoaXMuY3VycmVudExldmVsfWAsIGxldmVsSW5mbyk7XG4gICAgICAgIGNjLmxvZyhg5ZCO56uv6L+U5Zue55qEbGV2ZWxJZDogJHtsZXZlbEluZm8ubGV2ZWxJZH0sIGxldmVsTnVtYmVyOiAke2xldmVsSW5mby5sZXZlbE51bWJlcn1gKTtcblxuICAgICAgICB0aGlzLmN1cnJlbnRMZXZlbEluZm8gPSBsZXZlbEluZm87XG5cbiAgICAgICAgLy8g5pu05paw5Zyw6Zu35pWwVUlcbiAgICAgICAgdGhpcy51cGRhdGVNaW5lQ291bnRVSShsZXZlbEluZm8ubWluZUNvdW50KTtcblxuICAgICAgICAvLyDkvb/nlKjlvZPliY3orr7nva7nmoTlhbPljaHnvJblj7fvvIzogIzkuI3mmK/lkI7nq6/ov5Tlm57nmoRsZXZlbElkXG4gICAgICAgIC8vIOWboOS4uuWQjuerr+eahGxldmVsSWTlj6/og73kuI7liY3nq6/nmoTlhbPljaHnvJblj7fkuI3kuIDoh7RcbiAgICAgICAgY2MubG9nKGDkvb/nlKjlhbPljaHnvJblj7cgJHt0aGlzLmN1cnJlbnRMZXZlbH0g6L+b5YWl5YWz5Y2hYCk7XG4gICAgICAgIHRoaXMuZW50ZXJMZXZlbCh0aGlzLmN1cnJlbnRMZXZlbCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pu05paw5Zyw6Zu35pWwVUlcbiAgICAgKiBAcGFyYW0gbWluZUNvdW50IOWcsOmbt+aVsOmHj1xuICAgICAqL1xuICAgIHByaXZhdGUgdXBkYXRlTWluZUNvdW50VUkobWluZUNvdW50OiBudW1iZXIpIHtcbiAgICAgICAgaWYgKHRoaXMubWluZUNvdW50TGFiZWwpIHtcbiAgICAgICAgICAgIHRoaXMubWluZUNvdW50TGFiZWwuc3RyaW5nID0gbWluZUNvdW50LnRvU3RyaW5nKCk7XG4gICAgICAgICAgICBjYy5sb2coYOabtOaWsOWcsOmbt+aVsFVJOiAke21pbmVDb3VudH1gKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOagueaNruWFs+WNoeaVsOi/m+WFpeebuOW6lOeahOWFs+WNoVxuICAgICAqIEBwYXJhbSBsZXZlbE51bWJlciDlhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwcml2YXRlIGVudGVyTGV2ZWwobGV2ZWxOdW1iZXI6IG51bWJlcikge1xuICAgICAgICBjYy5sb2coYOi/m+WFpeWFs+WNoSAke2xldmVsTnVtYmVyfWApO1xuXG4gICAgICAgIC8vIOiwg+ivle+8muajgOafpeWcsOWbvuiKgueCueaYr+WQpuW3sumFjee9rlxuICAgICAgICBjYy5sb2coXCLlnLDlm77oioLngrnphY3nva7nirbmgIHvvJpcIik7XG4gICAgICAgIGNjLmxvZyhgcWlwYW44eDhOb2RlOiAke3RoaXMucWlwYW44eDhOb2RlID8gJ+KchScgOiAn4p2MJ31gKTtcbiAgICAgICAgY2MubG9nKGBxaXBhbjh4OU5vZGU6ICR7dGhpcy5xaXBhbjh4OU5vZGUgPyAn4pyFJyA6ICfinYwnfWApO1xuICAgICAgICBjYy5sb2coYHFpcGFuOXg5Tm9kZTogJHt0aGlzLnFpcGFuOXg5Tm9kZSA/ICfinIUnIDogJ+KdjCd9YCk7XG4gICAgICAgIGNjLmxvZyhgcWlwYW45eDEwTm9kZTogJHt0aGlzLnFpcGFuOXgxME5vZGUgPyAn4pyFJyA6ICfinYwnfWApO1xuICAgICAgICBjYy5sb2coYHFpcGFuMTB4MTBOb2RlOiAke3RoaXMucWlwYW4xMHgxME5vZGUgPyAn4pyFJyA6ICfinYwnfWApO1xuICAgICAgICBjYy5sb2coYGxldmVsUzAwMU5vZGU6ICR7dGhpcy5sZXZlbFMwMDFOb2RlID8gJ+KchScgOiAn4p2MJ31gKTtcblxuICAgICAgICAvLyDlhYjpmpDol4/miYDmnInlnLDlm77lrrnlmahcbiAgICAgICAgdGhpcy5oaWRlQWxsTWFwQ29udGFpbmVycygpO1xuXG4gICAgICAgIC8vIOagueaNruWFs+WNoeaVsOaYvuekuuWvueW6lOeahOWcsOWbvuiKgueCuVxuICAgICAgICBpZiAobGV2ZWxOdW1iZXIgPj0gMSAmJiBsZXZlbE51bWJlciA8PSA0KSB7XG4gICAgICAgICAgICAvLyDnrKwxLTTlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW44KjhcbiAgICAgICAgICAgIHRoaXMuc2hvd0dhbWVNYXAxKCk7XG4gICAgICAgICAgICB0aGlzLnNob3dNYXBOb2RlKHRoaXMucWlwYW44eDhOb2RlLCBcInFpcGFuOCo4XCIpO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSA1KSB7XG4gICAgICAgICAgICAvLyDnrKw15YWz77yM5omT5byAbGV2ZWxfcGFnZS9nYW1lX21hcF8yL2dhbWVfYmcvTGV2ZWxfUzAwMVxuICAgICAgICAgICAgdGhpcy5zaG93R2FtZU1hcDIoKTtcbiAgICAgICAgICAgIHRoaXMuc2hvd01hcE5vZGUodGhpcy5sZXZlbFMwMDFOb2RlLCBcIkxldmVsX1MwMDFcIik7XG4gICAgICAgIH0gZWxzZSBpZiAobGV2ZWxOdW1iZXIgPj0gNiAmJiBsZXZlbE51bWJlciA8PSA5KSB7XG4gICAgICAgICAgICAvLyDnrKw2LTnlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW44KjlcbiAgICAgICAgICAgIHRoaXMuc2hvd0dhbWVNYXAxKCk7XG4gICAgICAgICAgICB0aGlzLnNob3dNYXBOb2RlKHRoaXMucWlwYW44eDlOb2RlLCBcInFpcGFuOCo5XCIpO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAxMCkge1xuICAgICAgICAgICAgLy8g56ysMTDlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDAyXG4gICAgICAgICAgICB0aGlzLnNob3dHYW1lTWFwMigpO1xuICAgICAgICAgICAgdGhpcy5zaG93TWFwTm9kZSh0aGlzLmxldmVsUzAwMk5vZGUsIFwiTGV2ZWxfUzAwMlwiKTtcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA+PSAxMSAmJiBsZXZlbE51bWJlciA8PSAxNCkge1xuICAgICAgICAgICAgLy8g56ysMTEtMTTlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW45KjlcbiAgICAgICAgICAgIHRoaXMuc2hvd0dhbWVNYXAxKCk7XG4gICAgICAgICAgICB0aGlzLnNob3dNYXBOb2RlKHRoaXMucWlwYW45eDlOb2RlLCBcInFpcGFuOSo5XCIpO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAxNSkge1xuICAgICAgICAgICAgLy8g56ysMTXlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDAzXG4gICAgICAgICAgICB0aGlzLnNob3dHYW1lTWFwMigpO1xuICAgICAgICAgICAgdGhpcy5zaG93TWFwTm9kZSh0aGlzLmxldmVsUzAwM05vZGUsIFwiTGV2ZWxfUzAwM1wiKTtcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA+PSAxNiAmJiBsZXZlbE51bWJlciA8PSAxOSkge1xuICAgICAgICAgICAgLy8g56ysMTYtMTnlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW45KjEwXG4gICAgICAgICAgICB0aGlzLnNob3dHYW1lTWFwMSgpO1xuICAgICAgICAgICAgdGhpcy5zaG93TWFwTm9kZSh0aGlzLnFpcGFuOXgxME5vZGUsIFwicWlwYW45KjEwXCIpO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAyMCkge1xuICAgICAgICAgICAgLy8g56ysMjDlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDA0XG4gICAgICAgICAgICB0aGlzLnNob3dHYW1lTWFwMigpO1xuICAgICAgICAgICAgdGhpcy5zaG93TWFwTm9kZSh0aGlzLmxldmVsUzAwNE5vZGUsIFwiTGV2ZWxfUzAwNFwiKTtcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA+PSAyMSAmJiBsZXZlbE51bWJlciA8PSAyNCkge1xuICAgICAgICAgICAgLy8g56ysMjEtMjTlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW4xMCoxMFxuICAgICAgICAgICAgdGhpcy5zaG93R2FtZU1hcDEoKTtcbiAgICAgICAgICAgIHRoaXMuc2hvd01hcE5vZGUodGhpcy5xaXBhbjEweDEwTm9kZSwgXCJxaXBhbjEwKjEwXCIpO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAyNSkge1xuICAgICAgICAgICAgLy8g56ysMjXlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDA1XG4gICAgICAgICAgICB0aGlzLnNob3dHYW1lTWFwMigpO1xuICAgICAgICAgICAgdGhpcy5zaG93TWFwTm9kZSh0aGlzLmxldmVsUzAwNU5vZGUsIFwiTGV2ZWxfUzAwNVwiKTtcbiAgICAgICAgfSBlbHNlIGlmIChsZXZlbE51bWJlciA+PSAyNiAmJiBsZXZlbE51bWJlciA8PSAyOSkge1xuICAgICAgICAgICAgLy8g56ysMjYtMjnlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzEvY2hlc3NfYmcvcWlwYW4xMCoxMFxuICAgICAgICAgICAgdGhpcy5zaG93R2FtZU1hcDEoKTtcbiAgICAgICAgICAgIHRoaXMuc2hvd01hcE5vZGUodGhpcy5xaXBhbjEweDEwTm9kZSwgXCJxaXBhbjEwKjEwXCIpO1xuICAgICAgICB9IGVsc2UgaWYgKGxldmVsTnVtYmVyID09PSAzMCkge1xuICAgICAgICAgICAgLy8g56ysMzDlhbPvvIzmiZPlvIBsZXZlbF9wYWdlL2dhbWVfbWFwXzIvZ2FtZV9iZy9MZXZlbF9TMDA2XG4gICAgICAgICAgICB0aGlzLnNob3dHYW1lTWFwMigpO1xuICAgICAgICAgICAgdGhpcy5zaG93TWFwTm9kZSh0aGlzLmxldmVsUzAwNk5vZGUsIFwiTGV2ZWxfUzAwNlwiKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNjLndhcm4oYOacquefpeeahOWFs+WNoee8luWPtzogJHtsZXZlbE51bWJlcn1gKTtcbiAgICAgICAgfVxuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOaYvuekuuaMh+WumueahOWcsOWbvuiKgueCuVxuICAgICAqIEBwYXJhbSBtYXBOb2RlIOimgeaYvuekuueahOWcsOWbvuiKgueCuVxuICAgICAqIEBwYXJhbSBtYXBOYW1lIOWcsOWbvuWQjeensO+8iOeUqOS6juaXpeW/l++8iVxuICAgICAqL1xuICAgIHByaXZhdGUgc2hvd01hcE5vZGUobWFwTm9kZTogY2MuTm9kZSwgbWFwTmFtZTogc3RyaW5nKSB7XG4gICAgICAgIGlmIChtYXBOb2RlKSB7XG4gICAgICAgICAgICBjYy5sb2coYPCflI0g5YeG5aSH5pi+56S65Zyw5Zu+6IqC54K5OiAke21hcE5hbWV9YCk7XG4gICAgICAgICAgICBjYy5sb2coYOiKgueCuei3r+W+hDogJHttYXBOb2RlLm5hbWV9YCk7XG4gICAgICAgICAgICBjYy5sb2coYOiKgueCueW9k+WJjeeKtuaAgSAtIGFjdGl2ZTogJHttYXBOb2RlLmFjdGl2ZX0sIHBhcmVudDogJHttYXBOb2RlLnBhcmVudCA/IG1hcE5vZGUucGFyZW50Lm5hbWUgOiAnbnVsbCd9YCk7XG5cbiAgICAgICAgICAgIG1hcE5vZGUuYWN0aXZlID0gdHJ1ZTtcblxuICAgICAgICAgICAgLy8g5qOA5p+l54i26IqC54K56ZO+5piv5ZCm6YO95piv5r+A5rS754q25oCBXG4gICAgICAgICAgICBsZXQgY3VycmVudE5vZGUgPSBtYXBOb2RlLnBhcmVudDtcbiAgICAgICAgICAgIGxldCBwYXJlbnRDaGFpbiA9IFtdO1xuICAgICAgICAgICAgd2hpbGUgKGN1cnJlbnROb2RlKSB7XG4gICAgICAgICAgICAgICAgcGFyZW50Q2hhaW4ucHVzaChgJHtjdXJyZW50Tm9kZS5uYW1lfSgke2N1cnJlbnROb2RlLmFjdGl2ZX0pYCk7XG4gICAgICAgICAgICAgICAgY3VycmVudE5vZGUgPSBjdXJyZW50Tm9kZS5wYXJlbnQ7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYy5sb2coYOeItuiKgueCuemTvjogJHtwYXJlbnRDaGFpbi5qb2luKCcgLT4gJyl9YCk7XG5cbiAgICAgICAgICAgIGNjLmxvZyhg4pyFIOaYvuekuuWcsOWbvuiKgueCuTogJHttYXBOYW1lfSAtIOiuvue9ruWQjueKtuaAgTogJHttYXBOb2RlLmFjdGl2ZX1gKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNjLndhcm4oYOKdjCDlnLDlm77oioLngrnmnKrmib7liLA6ICR7bWFwTmFtZX1gKTtcbiAgICAgICAgICAgIGNjLndhcm4oYOivt+WcqOe8lui+keWZqOS4reS4uiBMZXZlbFBhZ2VDb250cm9sbGVyIOmFjee9riAke21hcE5hbWV9IOiKgueCueWxnuaAp2ApO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6ZqQ6JeP5omA5pyJ5Zyw5Zu+6IqC54K5XG4gICAgICovXG4gICAgcHJpdmF0ZSBoaWRlQWxsTWFwTm9kZXMoKSB7XG4gICAgICAgIGNvbnN0IGFsbE1hcE5vZGVzID0gW1xuICAgICAgICAgICAgdGhpcy5xaXBhbjh4OE5vZGUsXG4gICAgICAgICAgICB0aGlzLnFpcGFuOHg5Tm9kZSxcbiAgICAgICAgICAgIHRoaXMucWlwYW45eDlOb2RlLFxuICAgICAgICAgICAgdGhpcy5xaXBhbjl4MTBOb2RlLFxuICAgICAgICAgICAgdGhpcy5xaXBhbjEweDEwTm9kZSxcbiAgICAgICAgICAgIHRoaXMubGV2ZWxTMDAxTm9kZSxcbiAgICAgICAgICAgIHRoaXMubGV2ZWxTMDAyTm9kZSxcbiAgICAgICAgICAgIHRoaXMubGV2ZWxTMDAzTm9kZSxcbiAgICAgICAgICAgIHRoaXMubGV2ZWxTMDA0Tm9kZSxcbiAgICAgICAgICAgIHRoaXMubGV2ZWxTMDA1Tm9kZSxcbiAgICAgICAgICAgIHRoaXMubGV2ZWxTMDA2Tm9kZVxuICAgICAgICBdO1xuXG4gICAgICAgIGFsbE1hcE5vZGVzLmZvckVhY2gobm9kZSA9PiB7XG4gICAgICAgICAgICBpZiAobm9kZSkge1xuICAgICAgICAgICAgICAgIG5vZGUuYWN0aXZlID0gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOiuvue9ruW9k+WJjeWFs+WNoe+8iOS7juWklumDqOiwg+eUqO+8iVxuICAgICAqIEBwYXJhbSBsZXZlbE51bWJlciDlhbPljaHnvJblj7dcbiAgICAgKi9cbiAgICBwdWJsaWMgc2V0Q3VycmVudExldmVsKGxldmVsTnVtYmVyOiBudW1iZXIpIHtcbiAgICAgICAgdGhpcy5jdXJyZW50TGV2ZWwgPSBsZXZlbE51bWJlcjtcbiAgICAgICAgY2MubG9nKGDorr7nva7lvZPliY3lhbPljaE6ICR7bGV2ZWxOdW1iZXJ9YCk7XG5cbiAgICAgICAgLy8g56uL5Y2z5qC55o2u5YWz5Y2h5pWw5YiH5o2i5Zyw5Zu+5pi+56S6XG4gICAgICAgIHRoaXMuZW50ZXJMZXZlbChsZXZlbE51bWJlcik7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6I635Y+W5b2T5YmN5YWz5Y2h57yW5Y+3XG4gICAgICovXG4gICAgcHVibGljIGdldEN1cnJlbnRMZXZlbCgpOiBudW1iZXIge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50TGV2ZWw7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog6I635Y+W5b2T5YmN5YWz5Y2h5L+h5oGvXG4gICAgICovXG4gICAgcHVibGljIGdldEN1cnJlbnRMZXZlbEluZm8oKTogRXh0ZW5kTGV2ZWxJbmZvUmVzcG9uc2Uge1xuICAgICAgICByZXR1cm4gdGhpcy5jdXJyZW50TGV2ZWxJbmZvO1xuICAgIH1cblxuICAgIC8qKlxuICAgICAqIOmakOiXj+aJgOacieWcsOWbvuWuueWZqFxuICAgICAqL1xuICAgIHByaXZhdGUgaGlkZUFsbE1hcENvbnRhaW5lcnMoKSB7XG4gICAgICAgIC8vIOehruS/nSBsZXZlbF9wYWdlIOiKgueCueaYr+a/gOa0u+eahFxuICAgICAgICBpZiAodGhpcy5sZXZlbFBhZ2VOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmxldmVsUGFnZU5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgIGNjLmxvZyhcIuKchSDnoa7kv50gbGV2ZWxfcGFnZSDoioLngrnmv4DmtLtcIik7XG4gICAgICAgIH1cblxuICAgICAgICAvLyDpmpDol4/kuKTkuKrkuLvopoHnmoTlnLDlm77lrrnlmahcbiAgICAgICAgaWYgKHRoaXMuZ2FtZU1hcDFOb2RlKSB7XG4gICAgICAgICAgICB0aGlzLmdhbWVNYXAxTm9kZS5hY3RpdmUgPSBmYWxzZTtcbiAgICAgICAgICAgIGNjLmxvZyhcIumakOiXjyBnYW1lX21hcF8xIOWuueWZqFwiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5nYW1lTWFwMk5vZGUpIHtcbiAgICAgICAgICAgIHRoaXMuZ2FtZU1hcDJOb2RlLmFjdGl2ZSA9IGZhbHNlO1xuICAgICAgICAgICAgY2MubG9nKFwi6ZqQ6JePIGdhbWVfbWFwXzIg5a655ZmoXCIpO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5ZCM5pe26ZqQ6JeP5omA5pyJ5YW35L2T55qE5Zyw5Zu+6IqC54K5XG4gICAgICAgIHRoaXMuaGlkZUFsbE1hcE5vZGVzKCk7XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S6IGdhbWVfbWFwXzEg5a655Zmo77yI5pa55b2i5Zyw5Zu+77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93R2FtZU1hcDEoKSB7XG4gICAgICAgIGlmICh0aGlzLmdhbWVNYXAxTm9kZSkge1xuICAgICAgICAgICAgdGhpcy5nYW1lTWFwMU5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgIGNjLmxvZyhcIuKchSDmmL7npLogZ2FtZV9tYXBfMSDlrrnlmahcIik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjYy53YXJuKFwi4p2MIGdhbWVfbWFwXzEg6IqC54K55pyq5om+5YiwXCIpO1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgLyoqXG4gICAgICog5pi+56S6IGdhbWVfbWFwXzIg5a655Zmo77yI54m55q6K5YWz5Y2h77yJXG4gICAgICovXG4gICAgcHJpdmF0ZSBzaG93R2FtZU1hcDIoKSB7XG4gICAgICAgIGlmICh0aGlzLmdhbWVNYXAyTm9kZSkge1xuICAgICAgICAgICAgdGhpcy5nYW1lTWFwMk5vZGUuYWN0aXZlID0gdHJ1ZTtcbiAgICAgICAgICAgIGNjLmxvZyhcIuKchSDmmL7npLogZ2FtZV9tYXBfMiDlrrnlmahcIik7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjYy53YXJuKFwi4p2MIGdhbWVfbWFwXzIg6IqC54K55pyq5om+5YiwXCIpO1xuICAgICAgICB9XG4gICAgfVxufVxuIl19