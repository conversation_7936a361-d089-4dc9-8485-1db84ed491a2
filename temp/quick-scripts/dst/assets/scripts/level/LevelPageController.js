
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + this.currentLevel);
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelNumber: this.currentLevel
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        cc.log("\u6536\u5230\u5173\u5361\u4FE1\u606F - \u5F53\u524D\u5173\u5361 " + this.currentLevel, levelInfo);
        cc.log("\u540E\u7AEF\u8FD4\u56DE\u7684levelId: " + levelInfo.levelId + ", levelNumber: " + levelInfo.levelNumber);
        this.currentLevelInfo = levelInfo;
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        cc.log("\u4F7F\u7528\u5173\u5361\u7F16\u53F7 " + this.currentLevel + " \u8FDB\u5165\u5173\u5361");
        this.enterLevel(this.currentLevel);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
            cc.log("\u66F4\u65B0\u5730\u96F7\u6570UI: " + mineCount);
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        cc.log("\u8FDB\u5165\u5173\u5361 " + levelNumber);
        // 调试：检查地图节点是否已配置
        cc.log("地图节点配置状态：");
        cc.log("qipan8x8Node: " + (this.qipan8x8Node ? '✅' : '❌'));
        cc.log("qipan8x9Node: " + (this.qipan8x9Node ? '✅' : '❌'));
        cc.log("qipan9x9Node: " + (this.qipan9x9Node ? '✅' : '❌'));
        cc.log("qipan9x10Node: " + (this.qipan9x10Node ? '✅' : '❌'));
        cc.log("qipan10x10Node: " + (this.qipan10x10Node ? '✅' : '❌'));
        cc.log("levelS001Node: " + (this.levelS001Node ? '✅' : '❌'));
        // 先隐藏所有地图节点
        this.hideAllMapNodes();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
            cc.log("\u2705 \u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName);
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        this.currentLevel = levelNumber;
        cc.log("\u8BBE\u7F6E\u5F53\u524D\u5173\u5361: " + levelNumber);
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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