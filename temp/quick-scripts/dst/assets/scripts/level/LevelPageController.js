
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/level/LevelPageController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'c8e13uMxpxGELez69T2SfJx', 'LevelPageController');
// scripts/level/LevelPageController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var MessageId_1 = require("../net/MessageId");
var WebSocketManager_1 = require("../net/WebSocketManager");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var LevelPageController = /** @class */ (function (_super) {
    __extends(LevelPageController, _super);
    function LevelPageController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        // 开始游戏按钮
        _this.startGameButton = null;
        // 地雷数UI标签
        _this.mineCountLabel = null;
        // level_page节点
        _this.levelPageNode = null;
        // game_map_1节点
        _this.gameMap1Node = null;
        // game_map_2节点
        _this.gameMap2Node = null;
        // 方形地图节点引用
        _this.qipan8x8Node = null; // level_page/game_map_1/chess_bg/qipan8*8
        _this.qipan8x9Node = null; // level_page/game_map_1/chess_bg/qipan8*9
        _this.qipan9x9Node = null; // level_page/game_map_1/chess_bg/qipan9*9
        _this.qipan9x10Node = null; // level_page/game_map_1/chess_bg/qipan9*10
        _this.qipan10x10Node = null; // level_page/game_map_1/chess_bg/qipan10*10
        // 特殊关卡节点引用
        _this.levelS001Node = null; // level_page/game_map_2/game_bg/Level_S001
        _this.levelS002Node = null; // level_page/game_map_2/game_bg/Level_S002
        _this.levelS003Node = null; // level_page/game_map_2/game_bg/Level_S003
        _this.levelS004Node = null; // level_page/game_map_2/game_bg/Level_S004
        _this.levelS005Node = null; // level_page/game_map_2/game_bg/Level_S005
        _this.levelS006Node = null; // level_page/game_map_2/game_bg/Level_S006
        // 当前关卡数据
        _this.currentLevel = 1;
        _this.currentLevelInfo = null;
        return _this;
    }
    LevelPageController.prototype.onLoad = function () {
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
    };
    LevelPageController.prototype.start = function () {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    };
    /**
     * 开始游戏按钮点击事件
     */
    LevelPageController.prototype.onStartGameButtonClick = function () {
        cc.log("\u5F00\u59CB\u6E38\u620F - \u5173\u5361 " + this.currentLevel);
        // 发送ExtendLevelInfo消息到后端获取地图数据
        var request = {
            levelNumber: this.currentLevel
        };
        WebSocketManager_1.WebSocketManager.GetInstance().sendMsg(MessageId_1.MessageId.MsgTypeExtendLevelInfo, request);
    };
    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    LevelPageController.prototype.onExtendLevelInfo = function (levelInfo) {
        cc.log("\u6536\u5230\u5173\u5361\u4FE1\u606F - \u5173\u5361 " + levelInfo.levelNumber, levelInfo);
        this.currentLevelInfo = levelInfo;
        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);
        // 根据关卡数进入相应的关卡并打开相应的节点
        this.enterLevel(levelInfo.levelNumber);
    };
    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    LevelPageController.prototype.updateMineCountUI = function (mineCount) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
            cc.log("\u66F4\u65B0\u5730\u96F7\u6570UI: " + mineCount);
        }
    };
    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.enterLevel = function (levelNumber) {
        cc.log("\u8FDB\u5165\u5173\u5361 " + levelNumber);
        // 调试：检查地图节点是否已配置
        cc.log("地图节点配置状态：");
        cc.log("qipan8x8Node: " + (this.qipan8x8Node ? '✅' : '❌'));
        cc.log("qipan8x9Node: " + (this.qipan8x9Node ? '✅' : '❌'));
        cc.log("qipan9x9Node: " + (this.qipan9x9Node ? '✅' : '❌'));
        cc.log("qipan9x10Node: " + (this.qipan9x10Node ? '✅' : '❌'));
        cc.log("qipan10x10Node: " + (this.qipan10x10Node ? '✅' : '❌'));
        cc.log("levelS001Node: " + (this.levelS001Node ? '✅' : '❌'));
        // 先隐藏所有地图节点
        this.hideAllMapNodes();
        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        }
        else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showMapNode(this.levelS001Node, "Level_S001");
        }
        else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        }
        else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showMapNode(this.levelS002Node, "Level_S002");
        }
        else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        }
        else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showMapNode(this.levelS003Node, "Level_S003");
        }
        else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        }
        else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showMapNode(this.levelS004Node, "Level_S004");
        }
        else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showMapNode(this.levelS005Node, "Level_S005");
        }
        else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        }
        else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showMapNode(this.levelS006Node, "Level_S006");
        }
        else {
            cc.warn("\u672A\u77E5\u7684\u5173\u5361\u7F16\u53F7: " + levelNumber);
        }
    };
    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    LevelPageController.prototype.showMapNode = function (mapNode, mapName) {
        if (mapNode) {
            mapNode.active = true;
            cc.log("\u2705 \u663E\u793A\u5730\u56FE\u8282\u70B9: " + mapName);
        }
        else {
            cc.warn("\u274C \u5730\u56FE\u8282\u70B9\u672A\u627E\u5230: " + mapName);
            cc.warn("\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u4E3A LevelPageController \u914D\u7F6E " + mapName + " \u8282\u70B9\u5C5E\u6027");
        }
    };
    /**
     * 隐藏所有地图节点
     */
    LevelPageController.prototype.hideAllMapNodes = function () {
        var allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];
        allMapNodes.forEach(function (node) {
            if (node) {
                node.active = false;
            }
        });
    };
    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    LevelPageController.prototype.setCurrentLevel = function (levelNumber) {
        this.currentLevel = levelNumber;
        cc.log("\u8BBE\u7F6E\u5F53\u524D\u5173\u5361: " + levelNumber);
        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    };
    /**
     * 获取当前关卡编号
     */
    LevelPageController.prototype.getCurrentLevel = function () {
        return this.currentLevel;
    };
    /**
     * 获取当前关卡信息
     */
    LevelPageController.prototype.getCurrentLevelInfo = function () {
        return this.currentLevelInfo;
    };
    __decorate([
        property(cc.Button)
    ], LevelPageController.prototype, "startGameButton", void 0);
    __decorate([
        property(cc.Label)
    ], LevelPageController.prototype, "mineCountLabel", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelPageNode", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap1Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "gameMap2Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x8Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan8x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x9Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan9x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "qipan10x10Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS001Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS002Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS003Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS004Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS005Node", void 0);
    __decorate([
        property(cc.Node)
    ], LevelPageController.prototype, "levelS006Node", void 0);
    LevelPageController = __decorate([
        ccclass
    ], LevelPageController);
    return LevelPageController;
}(cc.Component));
exports.default = LevelPageController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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