// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { ExtendLevelInfoRequest, ExtendLevelInfoResponse } from "../bean/GameBean";
import { MessageId } from "../net/MessageId";
import { WebSocketManager } from "../net/WebSocketManager";

const { ccclass, property } = cc._decorator;

@ccclass
export default class LevelPageController extends cc.Component {

    // 开始游戏按钮
    @property(cc.Button)
    startGameButton: cc.Button = null;

    // 地雷数UI标签
    @property(cc.Label)
    mineCountLabel: cc.Label = null;

    // level_page节点
    @property(cc.Node)
    levelPageNode: cc.Node = null;

    // game_map_1节点
    @property(cc.Node)
    gameMap1Node: cc.Node = null;

    // game_map_2节点
    @property(cc.Node)
    gameMap2Node: cc.Node = null;

    // 方形地图节点引用
    @property(cc.Node)
    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8

    @property(cc.Node)
    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9

    @property(cc.Node)
    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9

    @property(cc.Node)
    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10

    @property(cc.Node)
    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10

    // 特殊关卡节点引用
    @property(cc.Node)
    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001

    @property(cc.Node)
    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002

    @property(cc.Node)
    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003

    @property(cc.Node)
    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004

    @property(cc.Node)
    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005

    @property(cc.Node)
    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006

    // 当前关卡数据
    private currentLevel: number = 1;
    private currentLevelInfo: ExtendLevelInfoResponse = null;

    onLoad() {
        // 设置开始游戏按钮点击事件
        if (this.startGameButton) {
            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);
        }
    }

    start() {
        // 初始化时隐藏所有地图节点
        this.hideAllMapNodes();
    }

    /**
     * 开始游戏按钮点击事件
     */
    private onStartGameButtonClick() {
        cc.log(`开始游戏 - 关卡 ${this.currentLevel}`);
        
        // 发送ExtendLevelInfo消息到后端获取地图数据
        const request: ExtendLevelInfoRequest = {
            levelNumber: this.currentLevel
        };
        
        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);
    }

    /**
     * 处理ExtendLevelInfo响应
     * @param levelInfo 关卡信息响应数据
     */
    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {
        cc.log(`收到关卡信息 - 当前关卡 ${this.currentLevel}`, levelInfo);
        cc.log(`后端返回的levelId: ${levelInfo.levelId}, levelNumber: ${levelInfo.levelNumber}`);

        this.currentLevelInfo = levelInfo;

        // 更新地雷数UI
        this.updateMineCountUI(levelInfo.mineCount);

        // 使用当前设置的关卡编号，而不是后端返回的levelId
        // 因为后端的levelId可能与前端的关卡编号不一致
        cc.log(`使用关卡编号 ${this.currentLevel} 进入关卡`);
        this.enterLevel(this.currentLevel);
    }

    /**
     * 更新地雷数UI
     * @param mineCount 地雷数量
     */
    private updateMineCountUI(mineCount: number) {
        if (this.mineCountLabel) {
            this.mineCountLabel.string = mineCount.toString();
            cc.log(`更新地雷数UI: ${mineCount}`);
        }
    }

    /**
     * 根据关卡数进入相应的关卡
     * @param levelNumber 关卡编号
     */
    private enterLevel(levelNumber: number) {
        cc.log(`进入关卡 ${levelNumber}`);

        // 调试：检查地图节点是否已配置
        cc.log("地图节点配置状态：");
        cc.log(`qipan8x8Node: ${this.qipan8x8Node ? '✅' : '❌'}`);
        cc.log(`qipan8x9Node: ${this.qipan8x9Node ? '✅' : '❌'}`);
        cc.log(`qipan9x9Node: ${this.qipan9x9Node ? '✅' : '❌'}`);
        cc.log(`qipan9x10Node: ${this.qipan9x10Node ? '✅' : '❌'}`);
        cc.log(`qipan10x10Node: ${this.qipan10x10Node ? '✅' : '❌'}`);
        cc.log(`levelS001Node: ${this.levelS001Node ? '✅' : '❌'}`);

        // 先隐藏所有地图容器
        this.hideAllMapContainers();

        // 根据关卡数显示对应的地图节点
        if (levelNumber >= 1 && levelNumber <= 4) {
            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8
            this.showGameMap1();
            this.showMapNode(this.qipan8x8Node, "qipan8*8");
        } else if (levelNumber === 5) {
            // 第5关，打开level_page/game_map_2/game_bg/Level_S001
            this.showGameMap2();
            this.showMapNode(this.levelS001Node, "Level_S001");
        } else if (levelNumber >= 6 && levelNumber <= 9) {
            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9
            this.showGameMap1();
            this.showMapNode(this.qipan8x9Node, "qipan8*9");
        } else if (levelNumber === 10) {
            // 第10关，打开level_page/game_map_2/game_bg/Level_S002
            this.showGameMap2();
            this.showMapNode(this.levelS002Node, "Level_S002");
        } else if (levelNumber >= 11 && levelNumber <= 14) {
            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9
            this.showGameMap1();
            this.showMapNode(this.qipan9x9Node, "qipan9*9");
        } else if (levelNumber === 15) {
            // 第15关，打开level_page/game_map_2/game_bg/Level_S003
            this.showGameMap2();
            this.showMapNode(this.levelS003Node, "Level_S003");
        } else if (levelNumber >= 16 && levelNumber <= 19) {
            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10
            this.showGameMap1();
            this.showMapNode(this.qipan9x10Node, "qipan9*10");
        } else if (levelNumber === 20) {
            // 第20关，打开level_page/game_map_2/game_bg/Level_S004
            this.showGameMap2();
            this.showMapNode(this.levelS004Node, "Level_S004");
        } else if (levelNumber >= 21 && levelNumber <= 24) {
            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        } else if (levelNumber === 25) {
            // 第25关，打开level_page/game_map_2/game_bg/Level_S005
            this.showGameMap2();
            this.showMapNode(this.levelS005Node, "Level_S005");
        } else if (levelNumber >= 26 && levelNumber <= 29) {
            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10
            this.showGameMap1();
            this.showMapNode(this.qipan10x10Node, "qipan10*10");
        } else if (levelNumber === 30) {
            // 第30关，打开level_page/game_map_2/game_bg/Level_S006
            this.showGameMap2();
            this.showMapNode(this.levelS006Node, "Level_S006");
        } else {
            cc.warn(`未知的关卡编号: ${levelNumber}`);
        }
    }

    /**
     * 显示指定的地图节点
     * @param mapNode 要显示的地图节点
     * @param mapName 地图名称（用于日志）
     */
    private showMapNode(mapNode: cc.Node, mapName: string) {
        if (mapNode) {
            cc.log(`🔍 准备显示地图节点: ${mapName}`);
            cc.log(`节点路径: ${mapNode.name}`);
            cc.log(`节点当前状态 - active: ${mapNode.active}, parent: ${mapNode.parent ? mapNode.parent.name : 'null'}`);

            mapNode.active = true;

            // 检查父节点链是否都是激活状态
            let currentNode = mapNode.parent;
            let parentChain = [];
            while (currentNode) {
                parentChain.push(`${currentNode.name}(${currentNode.active})`);
                currentNode = currentNode.parent;
            }
            cc.log(`父节点链: ${parentChain.join(' -> ')}`);

            cc.log(`✅ 显示地图节点: ${mapName} - 设置后状态: ${mapNode.active}`);
        } else {
            cc.warn(`❌ 地图节点未找到: ${mapName}`);
            cc.warn(`请在编辑器中为 LevelPageController 配置 ${mapName} 节点属性`);
        }
    }

    /**
     * 隐藏所有地图节点
     */
    private hideAllMapNodes() {
        const allMapNodes = [
            this.qipan8x8Node,
            this.qipan8x9Node,
            this.qipan9x9Node,
            this.qipan9x10Node,
            this.qipan10x10Node,
            this.levelS001Node,
            this.levelS002Node,
            this.levelS003Node,
            this.levelS004Node,
            this.levelS005Node,
            this.levelS006Node
        ];

        allMapNodes.forEach(node => {
            if (node) {
                node.active = false;
            }
        });
    }

    /**
     * 设置当前关卡（从外部调用）
     * @param levelNumber 关卡编号
     */
    public setCurrentLevel(levelNumber: number) {
        this.currentLevel = levelNumber;
        cc.log(`设置当前关卡: ${levelNumber}`);

        // 立即根据关卡数切换地图显示
        this.enterLevel(levelNumber);
    }

    /**
     * 获取当前关卡编号
     */
    public getCurrentLevel(): number {
        return this.currentLevel;
    }

    /**
     * 获取当前关卡信息
     */
    public getCurrentLevelInfo(): ExtendLevelInfoResponse {
        return this.currentLevelInfo;
    }

    /**
     * 隐藏所有地图容器
     */
    private hideAllMapContainers() {
        // 隐藏两个主要的地图容器
        if (this.gameMap1Node) {
            this.gameMap1Node.active = false;
            cc.log("隐藏 game_map_1 容器");
        }
        if (this.gameMap2Node) {
            this.gameMap2Node.active = false;
            cc.log("隐藏 game_map_2 容器");
        }

        // 同时隐藏所有具体的地图节点
        this.hideAllMapNodes();
    }

    /**
     * 显示 game_map_1 容器（方形地图）
     */
    private showGameMap1() {
        if (this.gameMap1Node) {
            this.gameMap1Node.active = true;
            cc.log("✅ 显示 game_map_1 容器");
        } else {
            cc.warn("❌ game_map_1 节点未找到");
        }
    }

    /**
     * 显示 game_map_2 容器（特殊关卡）
     */
    private showGameMap2() {
        if (this.gameMap2Node) {
            this.gameMap2Node.active = true;
            cc.log("✅ 显示 game_map_2 容器");
        } else {
            cc.warn("❌ game_map_2 节点未找到");
        }
    }
}
