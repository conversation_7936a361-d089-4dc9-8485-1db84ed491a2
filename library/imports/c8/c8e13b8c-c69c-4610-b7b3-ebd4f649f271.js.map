{"version": 3, "sources": ["assets/scripts/level/LevelPageController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,8CAA6C;AAC7C,4DAA2D;AAErD,IAAA,KAAwB,EAAE,CAAC,UAAU,EAAnC,OAAO,aAAA,EAAE,QAAQ,cAAkB,CAAC;AAG5C;IAAiD,uCAAY;IAA7D;QAAA,qEAmPC;QAjPG,SAAS;QAET,qBAAe,GAAc,IAAI,CAAC;QAElC,UAAU;QAEV,oBAAc,GAAa,IAAI,CAAC;QAEhC,eAAe;QAEf,mBAAa,GAAY,IAAI,CAAC;QAE9B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,eAAe;QAEf,kBAAY,GAAY,IAAI,CAAC;QAE7B,WAAW;QAEX,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,kBAAY,GAAY,IAAI,CAAC,CAAC,0CAA0C;QAGxE,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,oBAAc,GAAY,IAAI,CAAC,CAAC,4CAA4C;QAE5E,WAAW;QAEX,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAG1E,mBAAa,GAAY,IAAI,CAAC,CAAC,2CAA2C;QAE1E,SAAS;QACD,kBAAY,GAAW,CAAC,CAAC;QACzB,sBAAgB,GAA4B,IAAI,CAAC;;IAwL7D,CAAC;IAtLG,oCAAM,GAAN;QACI,eAAe;QACf,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,CAAC;SAC5E;IACL,CAAC;IAED,mCAAK,GAAL;QACI,eAAe;QACf,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED;;OAEG;IACK,oDAAsB,GAA9B;QACI,EAAE,CAAC,GAAG,CAAC,6CAAa,IAAI,CAAC,YAAc,CAAC,CAAC;QAEzC,+BAA+B;QAC/B,IAAM,OAAO,GAA2B;YACpC,WAAW,EAAE,IAAI,CAAC,YAAY;SACjC,CAAC;QAEF,mCAAgB,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,qBAAS,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;IACtF,CAAC;IAED;;;OAGG;IACI,+CAAiB,GAAxB,UAAyB,SAAkC;QACvD,EAAE,CAAC,GAAG,CAAC,qEAAiB,IAAI,CAAC,YAAc,EAAE,SAAS,CAAC,CAAC;QACxD,EAAE,CAAC,GAAG,CAAC,4CAAiB,SAAS,CAAC,OAAO,uBAAkB,SAAS,CAAC,WAAa,CAAC,CAAC;QAEpF,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;QAElC,UAAU;QACV,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAE5C,8BAA8B;QAC9B,4BAA4B;QAC5B,EAAE,CAAC,GAAG,CAAC,0CAAU,IAAI,CAAC,YAAY,8BAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IACvC,CAAC;IAED;;;OAGG;IACK,+CAAiB,GAAzB,UAA0B,SAAiB;QACvC,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,EAAE,CAAC;YAClD,EAAE,CAAC,GAAG,CAAC,uCAAY,SAAW,CAAC,CAAC;SACnC;IACL,CAAC;IAED;;;OAGG;IACK,wCAAU,GAAlB,UAAmB,WAAmB;QAClC,EAAE,CAAC,GAAG,CAAC,8BAAQ,WAAa,CAAC,CAAC;QAE9B,iBAAiB;QACjB,EAAE,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QACpB,EAAE,CAAC,GAAG,CAAC,oBAAiB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;QACzD,EAAE,CAAC,GAAG,CAAC,oBAAiB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;QACzD,EAAE,CAAC,GAAG,CAAC,oBAAiB,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;QACzD,EAAE,CAAC,GAAG,CAAC,qBAAkB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;QAC3D,EAAE,CAAC,GAAG,CAAC,sBAAmB,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;QAC7D,EAAE,CAAC,GAAG,CAAC,qBAAkB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAE,CAAC,CAAC;QAE3D,YAAY;QACZ,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,iBAAiB;QACjB,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YACtC,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAK,CAAC,EAAE;YAC1B,iDAAiD;YACjD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,EAAE;YAC7C,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,oDAAoD;YACpD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,qDAAqD;YACrD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;SACrD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,sDAAsD;YACtD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SACvD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM,IAAI,WAAW,IAAI,EAAE,IAAI,WAAW,IAAI,EAAE,EAAE;YAC/C,sDAAsD;YACtD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;SACvD;aAAM,IAAI,WAAW,KAAK,EAAE,EAAE;YAC3B,kDAAkD;YAClD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;SACtD;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,iDAAY,WAAa,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;;OAIG;IACK,yCAAW,GAAnB,UAAoB,OAAgB,EAAE,OAAe;QACjD,IAAI,OAAO,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC;YACtB,EAAE,CAAC,GAAG,CAAC,kDAAa,OAAS,CAAC,CAAC;SAClC;aAAM;YACH,EAAE,CAAC,IAAI,CAAC,wDAAc,OAAS,CAAC,CAAC;YACjC,EAAE,CAAC,IAAI,CAAC,iFAAkC,OAAO,8BAAO,CAAC,CAAC;SAC7D;IACL,CAAC;IAED;;OAEG;IACK,6CAAe,GAAvB;QACI,IAAM,WAAW,GAAG;YAChB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,YAAY;YACjB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,aAAa;SACrB,CAAC;QAEF,WAAW,CAAC,OAAO,CAAC,UAAA,IAAI;YACpB,IAAI,IAAI,EAAE;gBACN,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;aACvB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;OAGG;IACI,6CAAe,GAAtB,UAAuB,WAAmB;QACtC,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,EAAE,CAAC,GAAG,CAAC,2CAAW,WAAa,CAAC,CAAC;QAEjC,gBAAgB;QAChB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;IACjC,CAAC;IAED;;OAEG;IACI,6CAAe,GAAtB;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,iDAAmB,GAA1B;QACI,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC,CAAC;IA9OD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACc;IAIlC;QADC,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;+DACa;IAIhC;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAI9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAI7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;6DACW;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;+DACa;IAI/B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACY;IAvDb,mBAAmB;QADvC,OAAO;OACa,mBAAmB,CAmPvC;IAAD,0BAAC;CAnPD,AAmPC,CAnPgD,EAAE,CAAC,SAAS,GAmP5D;kBAnPoB,mBAAmB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { ExtendLevelInfoRequest, ExtendLevelInfoResponse } from \"../bean/GameBean\";\nimport { MessageId } from \"../net/MessageId\";\nimport { WebSocketManager } from \"../net/WebSocketManager\";\n\nconst { ccclass, property } = cc._decorator;\n\n@ccclass\nexport default class LevelPageController extends cc.Component {\n\n    // 开始游戏按钮\n    @property(cc.Button)\n    startGameButton: cc.Button = null;\n\n    // 地雷数UI标签\n    @property(cc.Label)\n    mineCountLabel: cc.Label = null;\n\n    // level_page节点\n    @property(cc.Node)\n    levelPageNode: cc.Node = null;\n\n    // game_map_1节点\n    @property(cc.Node)\n    gameMap1Node: cc.Node = null;\n\n    // game_map_2节点\n    @property(cc.Node)\n    gameMap2Node: cc.Node = null;\n\n    // 方形地图节点引用\n    @property(cc.Node)\n    qipan8x8Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*8\n\n    @property(cc.Node)\n    qipan8x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan8*9\n\n    @property(cc.Node)\n    qipan9x9Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*9\n\n    @property(cc.Node)\n    qipan9x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan9*10\n\n    @property(cc.Node)\n    qipan10x10Node: cc.Node = null; // level_page/game_map_1/chess_bg/qipan10*10\n\n    // 特殊关卡节点引用\n    @property(cc.Node)\n    levelS001Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S001\n\n    @property(cc.Node)\n    levelS002Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S002\n\n    @property(cc.Node)\n    levelS003Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S003\n\n    @property(cc.Node)\n    levelS004Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S004\n\n    @property(cc.Node)\n    levelS005Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S005\n\n    @property(cc.Node)\n    levelS006Node: cc.Node = null; // level_page/game_map_2/game_bg/Level_S006\n\n    // 当前关卡数据\n    private currentLevel: number = 1;\n    private currentLevelInfo: ExtendLevelInfoResponse = null;\n\n    onLoad() {\n        // 设置开始游戏按钮点击事件\n        if (this.startGameButton) {\n            this.startGameButton.node.on('click', this.onStartGameButtonClick, this);\n        }\n    }\n\n    start() {\n        // 初始化时隐藏所有地图节点\n        this.hideAllMapNodes();\n    }\n\n    /**\n     * 开始游戏按钮点击事件\n     */\n    private onStartGameButtonClick() {\n        cc.log(`开始游戏 - 关卡 ${this.currentLevel}`);\n        \n        // 发送ExtendLevelInfo消息到后端获取地图数据\n        const request: ExtendLevelInfoRequest = {\n            levelNumber: this.currentLevel\n        };\n        \n        WebSocketManager.GetInstance().sendMsg(MessageId.MsgTypeExtendLevelInfo, request);\n    }\n\n    /**\n     * 处理ExtendLevelInfo响应\n     * @param levelInfo 关卡信息响应数据\n     */\n    public onExtendLevelInfo(levelInfo: ExtendLevelInfoResponse) {\n        cc.log(`收到关卡信息 - 当前关卡 ${this.currentLevel}`, levelInfo);\n        cc.log(`后端返回的levelId: ${levelInfo.levelId}, levelNumber: ${levelInfo.levelNumber}`);\n\n        this.currentLevelInfo = levelInfo;\n\n        // 更新地雷数UI\n        this.updateMineCountUI(levelInfo.mineCount);\n\n        // 使用当前设置的关卡编号，而不是后端返回的levelId\n        // 因为后端的levelId可能与前端的关卡编号不一致\n        cc.log(`使用关卡编号 ${this.currentLevel} 进入关卡`);\n        this.enterLevel(this.currentLevel);\n    }\n\n    /**\n     * 更新地雷数UI\n     * @param mineCount 地雷数量\n     */\n    private updateMineCountUI(mineCount: number) {\n        if (this.mineCountLabel) {\n            this.mineCountLabel.string = mineCount.toString();\n            cc.log(`更新地雷数UI: ${mineCount}`);\n        }\n    }\n\n    /**\n     * 根据关卡数进入相应的关卡\n     * @param levelNumber 关卡编号\n     */\n    private enterLevel(levelNumber: number) {\n        cc.log(`进入关卡 ${levelNumber}`);\n\n        // 调试：检查地图节点是否已配置\n        cc.log(\"地图节点配置状态：\");\n        cc.log(`qipan8x8Node: ${this.qipan8x8Node ? '✅' : '❌'}`);\n        cc.log(`qipan8x9Node: ${this.qipan8x9Node ? '✅' : '❌'}`);\n        cc.log(`qipan9x9Node: ${this.qipan9x9Node ? '✅' : '❌'}`);\n        cc.log(`qipan9x10Node: ${this.qipan9x10Node ? '✅' : '❌'}`);\n        cc.log(`qipan10x10Node: ${this.qipan10x10Node ? '✅' : '❌'}`);\n        cc.log(`levelS001Node: ${this.levelS001Node ? '✅' : '❌'}`);\n\n        // 先隐藏所有地图节点\n        this.hideAllMapNodes();\n        \n        // 根据关卡数显示对应的地图节点\n        if (levelNumber >= 1 && levelNumber <= 4) {\n            // 第1-4关，打开level_page/game_map_1/chess_bg/qipan8*8\n            this.showMapNode(this.qipan8x8Node, \"qipan8*8\");\n        } else if (levelNumber === 5) {\n            // 第5关，打开level_page/game_map_2/game_bg/Level_S001\n            this.showMapNode(this.levelS001Node, \"Level_S001\");\n        } else if (levelNumber >= 6 && levelNumber <= 9) {\n            // 第6-9关，打开level_page/game_map_1/chess_bg/qipan8*9\n            this.showMapNode(this.qipan8x9Node, \"qipan8*9\");\n        } else if (levelNumber === 10) {\n            // 第10关，打开level_page/game_map_2/game_bg/Level_S002\n            this.showMapNode(this.levelS002Node, \"Level_S002\");\n        } else if (levelNumber >= 11 && levelNumber <= 14) {\n            // 第11-14关，打开level_page/game_map_1/chess_bg/qipan9*9\n            this.showMapNode(this.qipan9x9Node, \"qipan9*9\");\n        } else if (levelNumber === 15) {\n            // 第15关，打开level_page/game_map_2/game_bg/Level_S003\n            this.showMapNode(this.levelS003Node, \"Level_S003\");\n        } else if (levelNumber >= 16 && levelNumber <= 19) {\n            // 第16-19关，打开level_page/game_map_1/chess_bg/qipan9*10\n            this.showMapNode(this.qipan9x10Node, \"qipan9*10\");\n        } else if (levelNumber === 20) {\n            // 第20关，打开level_page/game_map_2/game_bg/Level_S004\n            this.showMapNode(this.levelS004Node, \"Level_S004\");\n        } else if (levelNumber >= 21 && levelNumber <= 24) {\n            // 第21-24关，打开level_page/game_map_1/chess_bg/qipan10*10\n            this.showMapNode(this.qipan10x10Node, \"qipan10*10\");\n        } else if (levelNumber === 25) {\n            // 第25关，打开level_page/game_map_2/game_bg/Level_S005\n            this.showMapNode(this.levelS005Node, \"Level_S005\");\n        } else if (levelNumber >= 26 && levelNumber <= 29) {\n            // 第26-29关，打开level_page/game_map_1/chess_bg/qipan10*10\n            this.showMapNode(this.qipan10x10Node, \"qipan10*10\");\n        } else if (levelNumber === 30) {\n            // 第30关，打开level_page/game_map_2/game_bg/Level_S006\n            this.showMapNode(this.levelS006Node, \"Level_S006\");\n        } else {\n            cc.warn(`未知的关卡编号: ${levelNumber}`);\n        }\n    }\n\n    /**\n     * 显示指定的地图节点\n     * @param mapNode 要显示的地图节点\n     * @param mapName 地图名称（用于日志）\n     */\n    private showMapNode(mapNode: cc.Node, mapName: string) {\n        if (mapNode) {\n            mapNode.active = true;\n            cc.log(`✅ 显示地图节点: ${mapName}`);\n        } else {\n            cc.warn(`❌ 地图节点未找到: ${mapName}`);\n            cc.warn(`请在编辑器中为 LevelPageController 配置 ${mapName} 节点属性`);\n        }\n    }\n\n    /**\n     * 隐藏所有地图节点\n     */\n    private hideAllMapNodes() {\n        const allMapNodes = [\n            this.qipan8x8Node,\n            this.qipan8x9Node,\n            this.qipan9x9Node,\n            this.qipan9x10Node,\n            this.qipan10x10Node,\n            this.levelS001Node,\n            this.levelS002Node,\n            this.levelS003Node,\n            this.levelS004Node,\n            this.levelS005Node,\n            this.levelS006Node\n        ];\n\n        allMapNodes.forEach(node => {\n            if (node) {\n                node.active = false;\n            }\n        });\n    }\n\n    /**\n     * 设置当前关卡（从外部调用）\n     * @param levelNumber 关卡编号\n     */\n    public setCurrentLevel(levelNumber: number) {\n        this.currentLevel = levelNumber;\n        cc.log(`设置当前关卡: ${levelNumber}`);\n\n        // 立即根据关卡数切换地图显示\n        this.enterLevel(levelNumber);\n    }\n\n    /**\n     * 获取当前关卡编号\n     */\n    public getCurrentLevel(): number {\n        return this.currentLevel;\n    }\n\n    /**\n     * 获取当前关卡信息\n     */\n    public getCurrentLevelInfo(): ExtendLevelInfoResponse {\n        return this.currentLevelInfo;\n    }\n}\n"]}